# blog/urls.py
from django.urls import path
from .views import PostListView, PostDetailView, PostUpdateView, edit_post

urlpatterns = [
    path('', PostListView.as_view(), name='post_list'),
    path('post/<int:pk>/', PostDetailView.as_view(), name='post_detail'),
    # using class-based view:
    path('post/<int:pk>/edit/', PostUpdateView.as_view(), name='post_edit'),
    # OR if you want function-based, replace the above line with:
    # path('post/<int:pk>/edit/', edit_post, name='post_edit'),
]
